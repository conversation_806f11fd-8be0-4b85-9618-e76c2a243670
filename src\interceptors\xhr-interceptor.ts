/**
 * XHR 拦截器
 * 负责拦截和监听页面的 XHR 请求和响应
 */

import { logger } from '../utils/helpers';
import type { ModelInfo } from '../types';

export class XHRInterceptor {
  private originalXHROpen: typeof XMLHttpRequest.prototype.open;
  private originalXHRSend: typeof XMLHttpRequest.prototype.send;
  private originalFetch: typeof window.fetch;
  private isInitialized: boolean = false;

  // 事件回调
  private onModelListReceived?: (models: ModelInfo[]) => void;
  private onStreamResponseReceived?: (data: any) => void;
  private onGenerateContentReceived?: (data: any) => void;

  constructor() {
    this.originalXHROpen = XMLHttpRequest.prototype.open;
    this.originalXHRSend = XMLHttpRequest.prototype.send;
    this.originalFetch = window.fetch;
  }

  /**
   * 初始化拦截器
   */
  public init(): void {
    if (this.isInitialized) {
      logger.warn('XHR 拦截器已经初始化');
      return;
    }

    this.interceptXHR();
    this.interceptFetch();
    this.isInitialized = true;
    logger.info('XHR 拦截器初始化完成');
  }

  /**
   * 设置模型列表接收回调
   */
  public onModelList(callback: (models: ModelInfo[]) => void): void {
    this.onModelListReceived = callback;
  }

  /**
   * 设置流式响应接收回调
   */
  public onStreamResponse(callback: (data: any) => void): void {
    this.onStreamResponseReceived = callback;
  }

  /**
   * 设置生成内容接收回调
   */
  public onGenerateContent(callback: (data: any) => void): void {
    this.onGenerateContentReceived = callback;
  }

  /**
   * 拦截 XMLHttpRequest
   */
  private interceptXHR(): void {
    const self = this;

    XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
      const urlStr = url.toString();
      
      // 监听响应
      this.addEventListener('readystatechange', function() {
        if (this.readyState === 4 && this.status === 200) {
          self.handleResponse(urlStr, this.responseText);
        }
      });

      return self.originalXHROpen.call(this, method, url, ...args);
    };

    XMLHttpRequest.prototype.send = function(...args: any[]) {
      return self.originalXHRSend.call(this, ...args);
    };
  }

  /**
   * 拦截 Fetch API
   */
  private interceptFetch(): void {
    const self = this;

    window.fetch = async function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
      const response = await self.originalFetch.call(this, input, init);
      
      // 克隆响应以便读取
      const clonedResponse = response.clone();
      const url = typeof input === 'string' ? input : input.toString();
      
      try {
        const text = await clonedResponse.text();
        self.handleResponse(url, text);
      } catch (error) {
        logger.debug('读取 fetch 响应失败:', error);
      }

      return response;
    };
  }

  /**
   * 处理响应数据
   */
  private handleResponse(url: string, responseText: string): void {
    try {
      // 拦截模型列表
      if (this.isModelListUrl(url)) {
        this.interceptModelList(responseText);
      }
      
      // 拦截生成内容响应
      if (this.isGenerateContentUrl(url)) {
        this.interceptGenerateContent(responseText);
      }
      
      // 拦截流式响应
      if (this.isStreamResponseUrl(url)) {
        this.interceptStreamResponse(responseText);
      }
    } catch (error) {
      logger.debug('处理响应数据失败:', error);
    }
  }

  /**
   * 判断是否为模型列表 URL
   */
  private isModelListUrl(url: string): boolean {
    return url.includes('/models') || url.includes('listModels');
  }

  /**
   * 判断是否为生成内容 URL
   */
  private isGenerateContentUrl(url: string): boolean {
    return url.includes('generateContent') || url.includes(':generateContent');
  }

  /**
   * 判断是否为流式响应 URL
   */
  private isStreamResponseUrl(url: string): boolean {
    return url.includes('streamGenerateContent') || url.includes(':streamGenerateContent');
  }

  /**
   * 拦截模型列表
   */
  public interceptModelList(responseText: string): void {
    try {
      const data = JSON.parse(responseText);
      
      if (data.models && Array.isArray(data.models)) {
        const models: ModelInfo[] = data.models.map((model: any) => ({
          id: model.name || model.id,
          name: model.name || model.id,
          displayName: model.displayName || model.name || model.id,
          description: model.description || '',
          version: model.version || '1.0',
          inputTokenLimit: model.inputTokenLimit || 1000000,
          outputTokenLimit: model.outputTokenLimit || 8192,
          supportedGenerationMethods: model.supportedGenerationMethods || ['generateContent'],
          temperature: model.temperature,
          topP: model.topP,
          topK: model.topK,
        }));

        logger.info(`拦截到模型列表: ${models.length} 个模型`);
        this.onModelListReceived?.(models);
      }
    } catch (error) {
      logger.error('解析模型列表失败:', error);
    }
  }

  /**
   * 拦截生成内容响应
   */
  public interceptGenerateContent(responseText: string): void {
    try {
      const data = JSON.parse(responseText);
      
      if (data.candidates && Array.isArray(data.candidates)) {
        const content = data.candidates[0]?.content?.parts?.[0]?.text || '';
        const finishReason = data.candidates[0]?.finishReason || 'stop';
        
        const responseData = {
          content,
          finishReason,
          usage: data.usageMetadata,
          rawResponse: data,
        };

        logger.info('拦截到生成内容响应');
        this.onGenerateContentReceived?.(responseData);
      }
    } catch (error) {
      logger.error('解析生成内容响应失败:', error);
    }
  }

  /**
   * 拦截流式响应
   */
  public interceptStreamResponse(responseText: string): void {
    try {
      // 处理流式响应（通常是多行 JSON）
      const lines = responseText.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const jsonStr = line.substring(6);
          if (jsonStr === '[DONE]') {
            continue;
          }
          
          try {
            const data = JSON.parse(jsonStr);
            
            if (data.candidates && Array.isArray(data.candidates)) {
              const content = data.candidates[0]?.content?.parts?.[0]?.text || '';
              const finishReason = data.candidates[0]?.finishReason;
              
              const streamData = {
                content,
                finishReason,
                delta: content,
                usage: data.usageMetadata,
                rawResponse: data,
              };

              this.onStreamResponseReceived?.(streamData);
            }
          } catch (parseError) {
            logger.debug('解析流式数据行失败:', parseError);
          }
        }
      }
    } catch (error) {
      logger.error('解析流式响应失败:', error);
    }
  }

  /**
   * 销毁拦截器
   */
  public destroy(): void {
    if (!this.isInitialized) {
      return;
    }

    XMLHttpRequest.prototype.open = this.originalXHROpen;
    XMLHttpRequest.prototype.send = this.originalXHRSend;
    window.fetch = this.originalFetch;
    
    this.isInitialized = false;
    logger.info('XHR 拦截器已销毁');
  }
}
