import logging
import multiprocessing
import os
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

logger = logging.getLogger(__name__)

# --- 全局状态 ---
STREAM_QUEUE: multiprocessing.Queue = None
STREAM_PROCESS: multiprocessing.Process = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global STREAM_QUEUE, STREAM_PROCESS

    logger.info("🚀 启动应用生命周期管理...")

    # 启动HTTPS代理服务器(如果启用)
    enable_https_proxy = os.environ.get("ENABLE_HTTPS_PROXY", "false").lower() == "true"
    if enable_https_proxy:
        try:
            from server.proxy import start_proxy_server

            STREAM_QUEUE = multiprocessing.Queue()
            STREAM_PROCESS = multiprocessing.Process(target=start_proxy_server, args=(STREAM_QUEUE,))
            STREAM_PROCESS.start()
            logger.info("✅ HTTPS代理服务器已启动")
        except ImportError:
            logger.warning("⚠️ HTTPS代理模块未找到，跳过代理服务器启动")
        except Exception as e:
            logger.error(f"❌ 启动HTTPS代理服务器失败: {e}")

    yield

    # 清理资源
    logger.info("🔄 清理应用资源...")
    if STREAM_PROCESS and STREAM_PROCESS.is_alive():
        logger.info("停止HTTPS代理服务器...")
        STREAM_PROCESS.terminate()
        STREAM_PROCESS.join(timeout=5)
        if STREAM_PROCESS.is_alive():
            STREAM_PROCESS.kill()
        logger.info("✅ HTTPS代理服务器已停止")


# --- FastAPI 应用实例 ---
app = FastAPI(
    title="Gemini Userscript Bridge API Server",
    description="通过 Userscript 与 Gemini Web 交互的代理服务器",
    version="1.0.0",
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境建议限制来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
